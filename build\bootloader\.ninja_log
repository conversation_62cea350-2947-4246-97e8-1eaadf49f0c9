# ninja log v6
34	265	7784796461514354	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	b8ba88626490e6d2
42	308	7784796461594369	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	25e11b047edc83c0
56	360	7784796461734339	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	937bd73ff42bf221
65	409	7784796461824384	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	73d2ea14d681591b
75	458	7784796461924354	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	649cdad656f8b4f8
83	493	7784796461994389	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	58f01bb682f2e3d7
92	526	7784796462094388	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	e1b94d12daf9ffd8
105	610	7784796462224391	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	cdfb41a536fb8bbd
117	654	7784796462334380	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	5584ae294dc8bc2c
129	686	7784796462454416	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	210943843935fd9b
146	724	7784796462634357	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	673ea9e469617ab
161	762	7784796462784412	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	483192b6cbcd8ea8
188	828	7784796463054344	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cfdd251618f6a714
228	859	7784796463444402	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	497155e73f89de4d
266	902	7784796463834355	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	14c31d09cb487174
309	922	7784796464254360	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	6aa3570ab1cd2ae2
361	939	7784796464774334	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ca32f6349de14497
50	961	7784796461674359	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	d0886aec47ac34ed
27	983	7784796461434336	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	30f51d844e329496
409	1002	7784796465264395	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	c0df74edbaa8d4e2
21	1017	7784796461384352	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	31643bd8ceda0e35
13	1031	7784796461294375	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	cb78176f3653dddc
459	1093	7784796465754339	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	4bd0cfcf61b4f9eb
494	1114	7784796466104386	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8a2798b39b38967
527	1149	7784796466434354	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	83e187d5649e4953
611	1184	7784796467274346	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	63f8478ca2cd3234
655	1261	7784796467714376	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	fda948f7d94318fe
686	1331	7784796468030071	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4824227fe51f023e
724	1379	7784796468410195	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	142b08fee5111a88
829	1419	7784796469460232	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4b64013a1931ce9e
859	1454	7784796469760242	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	bf5475bb65ab941c
902	1491	7784796470190226	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	647702da2488015a
922	1531	7784796470390205	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	83781b0edbf4dd06
939	1587	7784796470560193	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	dcae97481a03c066
962	1631	7784796470790216	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	fc218c3d43777ba7
983	1657	7784796471000231	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	70b39228ff3c3953
1002	1686	7784796471190177	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	6790c1df7306deb2
1017	1735	7784796471340220	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	b967448cdfaa7383
1031	1771	7784796471480225	esp-idf/log/liblog.a	c91a251e1ec6f52c
1094	1916	7784796472110246	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	606a76877fef8d81
1114	1954	7784796472310233	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a0cddf8baf8a7725
1149	1987	7784796472660255	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	ffdd27642721c0bf
1184	2014	7784796473010230	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	b77bc6b182498ad1
1261	2094	7784796473780213	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	4ebae100feb37f9a
1331	2143	7784796474480246	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	7a3f3769d1ee15cd
1379	2164	7784796474960222	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	233500e586f9ee34
1420	2185	7784796475370205	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	abca8987946c4
762	2216	7784796468790202	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	858d4baa72e0a17b
1455	2233	7784796475720216	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	889baa59c793157
1491	2261	7784796476085506	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	1b857d8603efa092
1531	2279	7784796476475513	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	b80ca273a9dbb954
1587	2292	7784796477045496	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	6e27940ab0c3e7c
1631	2314	7784796477475476	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	749b6846e469439c
1657	2324	7784796477735495	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	7337709c27fee1ca
1686	2344	7784796478028724	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	25b380721e1b29b6
1735	2369	7784796478518748	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	35c22edb96f4c16a
1771	2412	7784796478878729	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
1916	2533	7784796480338771	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	be38ffb92a960c1f
1954	2566	7784796480718718	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	28f496dae6971f53
1987	2587	7784796481043936	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	8f538409bc2cd6cc
2014	2610	7784796481303942	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61d58f48e37c570c
2095	2635	7784796482113947	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	a1902cbe249b4406
2143	2664	7784796482603981	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	3f7cd516dfa5aa97
2185	2685	7784796483024008	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	24015bfc507f4dc0
2217	2705	7784796483333971	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	885af7f345ad7210
2233	2729	7784796483504004	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	445508437b2f4d37
2261	2777	7784796483783985	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	6d24e56d1ded1fb9
2279	2856	7784796483963949	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	97f79caf25082d69
2292	2873	7784796484094005	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	1abbd206e65300d7
2314	2873	7784796484304030	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	29b8fde4d883d840
2325	2874	7784796484414004	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	754ba61bf0dd9e65
2345	2874	7784796484613994	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	d3b413fd7717610e
2369	2875	7784796484854018	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	2fbd10801eda5f93
2413	2875	7784796485293957	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
2533	2919	7784796486503951	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	9da01f23b4cebb82
2566	2919	7784796486833952	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	36a0b2d6be3ef1b
2587	2920	7784796487043963	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	58accd046d586db1
2610	2920	7784796487273976	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	e9fa507b2c68099a
2635	2921	7784796487523968	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	d880b4553289daf1
2664	2921	7784796487813949	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	1af531e95af75350
2685	2922	7784796488020637	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	88ce9b6940f96e5c
2705	2922	7784796488220639	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	7ab51ff84ae785a9
2729	2935	7784796488460669	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b9f0ed134b19d3fd
2778	2935	7784796490180642	project_elf_src_esp32c3.c	6c7d774e998bc721
2778	2935	7784796490180642	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	6c7d774e998bc721
2935	3040	7784796490520645	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	fadd1928e73520b8
2856	3068	7784796489730648	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	74d83039a25dbef3
2875	3121	7784796489920698	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
2164	3163	7784796482813984	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	2846994fd4688db
3121	3222	7784796492382668	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
3223	3323	7784796493397171	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
3323	3501	7784796494402436	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
3501	3583	7784796496181570	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
3583	3658	7784796497001571	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
3658	3753	7784796497751577	esp-idf/hal/libhal.a	38abfeb38a2a248d
3753	3838	7784796498707602	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
3838	3991	7784796499557555	esp-idf/soc/libsoc.a	2ed56a05eac453da
3991	4086	7784796501077547	esp-idf/main/libmain.a	5bc613d7954c6145
4086	4231	7784796502027529	bootloader.elf	fced577b480f0e0d
4231	4575	7784796506867576	.bin_timestamp	e514e3b0f06691b1
4231	4575	7784796506867576	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	e514e3b0f06691b1
4575	4684	7784796506917554	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
4575	4684	7784796506917554	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
10	109	7784800320803331	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
10	109	7784800320803331	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
13	108	7784802169062012	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
13	108	7784802169062012	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
