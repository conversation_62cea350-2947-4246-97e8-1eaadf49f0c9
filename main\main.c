#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"

#include "ble_init.h"
#include "wifi_ap.h"
#include "file_server.h"
#include "led_charge.h"
#include "dev_id.h"
#include "dev_power.h"
#include "data_collector.h"
#include "version.h"
#include "sensor_test.h"

//
#define TAG "[TMP]"

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ret = nvs_flash_erase();
        ESP_ERROR_CHECK(ret);
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    /*
    // 初始化设备ID模块 - 需要在打印版本信息之前初始化
    ret = dev_id_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize device ID: %s", esp_err_to_name(ret));
        return;
    }


    // 打印版本信息 - 现在可以显示设备MAC地址
    print_version_info();

    // 初始化BLE模块
    ret = ble_task();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize BLE: %s", esp_err_to_name(ret));
        return;
    }
 */
    // 自动采集数据到缓冲区
    ret = data_collector_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize data collector: %s", esp_err_to_name(ret));
        return;
    }

    /*
    // 初始化电源模块（按键和使能引脚）
    ret = dev_power_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize power: %s", esp_err_to_name(ret));
        return;
    }

    // 启动LED充电监控任务 (一键启动：自动初始化+创建任务+OTA控制+文件系统挂载)
    ret = led_charge_task();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start LED charge task: %s", esp_err_to_name(ret));
        return;
    }
*/

    ESP_LOGI(TAG, "System initialization completed successfully");
}
